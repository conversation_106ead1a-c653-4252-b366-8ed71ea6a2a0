import { createRouter, createWebHistory } from 'vue-router';
import { docCookies } from '@/utils/cookieop';
import { toLogin } from '@/utils/gopage';
import Cookies from 'js-cookie';
import { userInfoStore } from '@/stores/userInfo';
import { useUserStore } from '@/stores/user';
import { getUserDetailApi } from '@/apis/learnStatistics';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/home',
      component: () => import('@/layout/index.vue'),
      children: [
        {
          path: '/home',
          name: 'home',
          component: () => import('@/views/home/<USER>')
        },
        {
          path: '/questionDetail',
          name: 'questionDetail',
          component: () => import('@/views/question/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/learnStatistics',
          name: 'learnStatistics',
          component: () => import('@/views/learnStatistics/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/goodIntroduce',
          name: 'goodIntroduce',
          component: () => import('@/views/good/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/case',
          name: 'case',
          component: () => import('@/views/case/index.vue'),
          redirect: '/case/exam',
          children: [
            {
              path: 'map',
              name: 'case-map',
              component: () => import('@/views/case/map/index.vue'),
              meta: {
                keepAlive: true,
                requiresAuth: true
              }
            },
            {
              path: 'process',
              name: 'case-process',
              component: () => import('@/views/case/process/index.vue'),
              meta: {
                keepAlive: true,
                requiresAuth: true
              }
            },
            {
              path: 'exam',
              name: 'case-exam',
              component: () => import('@/views/case/exam/index.vue'),
              meta: {
                keepAlive: true,
                requiresAuth: true
              }
            },
            {
              path: 'collect',
              name: 'case-collect',
              component: () => import('@/views/case/collect/index.vue'),
              meta: {
                keepAlive: true,
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: '/klgexplain',
          name: 'klgexplain',
          component: () => import('@/views/klgexplain/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/exam',
          name: 'exam',
          component: () => import('@/views/exam/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/learning',
          name: 'learning',
          component: () => import('@/views/learning/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/classification',
          name: 'classification',
          component: () => import('@/views/classification/index.vue')
        },
        {
          path: '/knowledge',
          name: 'knowledge',
          component: () => import('@/views/knowledge/index.vue')
        },
        {
          path: '/arealearning',
          name: 'arealearning',
          component: () => import('@/views/arealearning/index.vue')
          // meta: {
          //   requiresAuth: true
          // }
        },
        {
          path: '/arealearning/prj',
          name: 'arealearning-prj',
          component: () => import('@/views/arealearning/prj.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/shoppinglist',
          name: 'shoppinglist',
          component: () => import('@/views/shoppinglist/index.vue'),
          redirect: '/shoppinglist/order/prjorder',
          children: [
            {
              path: 'order/:id',
              name: 'shoppinglist-order',
              component: () => import('@/views/shoppinglist/order/index.vue'),
              meta: {
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: '/message',
          name: 'message',
          component: () => import('@/views/message/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/userspace',
          name: 'userspace',
          component: () => import('@/views/userspace/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/klgdetail',
          name: 'klgdetail',
          component: () => import('@/views/klgdetail/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/areadetail',
          name: 'areadetail',
          component: () => import('@/views/areadetail/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/area',
          name: 'area',
          component: () => import('@/views/area/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/viparea',
          name: 'viparea',
          component: () => import('@/views/vipgood/areadetail.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/agreement',
          name: 'agreement',
          component: () => import('@/views/agreement/index.vue'),
          meta: {
            requiresAuth: true
          }
        },
        {
          path: '/mystudy',
          name: 'mystudy',
          component: () => import('@/views/mystudy/index.vue'),
          meta: {
            requiresAuth: true
          },
          children: [
            {
              path: 'map',
              name: 'mystudy-map',
              component: () => import('@/views/mystudy/map/index.vue')
            },
            {
              path: 'question',
              name: 'mystudy-question',
              component: () => import('@/views/mystudy/question/index.vue')
            },
            {
              path: 'collect',
              name: 'mystudy-collect',
              component: () => import('@/views/mystudy/collect/index.vue')
            },
            {
              path: 'detail',
              name: 'collect-detail',
              component: () => import('@/views/mystudy/collect/detail.vue')
            }
          ]
        },
        {
          path: '/aboutus',
          name: 'aboutus',
          component: () => import('@/views/aboutus/index.vue')
        },
        {
          path: '/test/code-width',
          name: 'code-width-test',
          component: () => import('@/views/test/CodeWidthTest.vue')
        },
        {
          path: '/helpcenter',
          name: 'helpcenter',
          component: () => import('@/views/helpcenter/index.vue')
        }
      ]
    },

    {
      path: '/:catchAll(.*)',
      redirect: '/home'
    }
  ],
  scrollBehavior(_to, _from, _savedPosition) {
    return { top: 0 };
  }
});

// 用户认证守卫
router.beforeEach((_to, _from, next) => {
  const userinfo = userInfoStore();
  const userStore = useUserStore();
  const islogin = userinfo.getUserId();
  //不登陆就可以访问的路径
  const whiteList = [
    '/home',
    '/classification',
    '/knowledge',
    '/aboutus',
    '/helpcenter',
    '/goodIntroduce'
  ];
  if (islogin) {
    next();
  } else {
    userinfo.getUserInfo().then(async (valid) => {
      if (valid && !userStore.userInfo.uniqueCode) {
        const res = await getUserDetailApi();
        if (res.success) {
          userStore.setUserInfo(res.data);
          next();
        }
      } else {
        if (whiteList.includes(_to.path)) {
          // 白名单路径：允许访问
          next();
        } else {
          const url = new URL(window.location.href);
          const tarUrl = encodeURIComponent(url.host + url.pathname + url.search + url.hash);
          //todo：跳回
          window.open(`https://user.endlessorigin.com/login?redirect=${tarUrl}`, '_self');
        }
      }
    });
  }
});

// 动态重定向到mystudy的第一个子路由
router.beforeEach((to, _from, next) => {
  if ((to.path === '/mystudy' || to.path === '/mystudy/') && to.name === 'mystudy') {
    // 获取所有路由
    const routes = router.getRoutes();
    // 找到mystudy路由
    const mystudyRoute = routes.find((route) => route.name === 'mystudy');
    if (mystudyRoute && mystudyRoute.children && mystudyRoute.children.length > 0) {
      // 获取第一个子路由
      const firstChild = mystudyRoute.children[0];
      // 重定向到第一个子路由
      next({ name: firstChild.name });
    } else {
      console.log('未找到mystudy子路由');
      next();
    }
  } else {
    next();
  }
});

export default router;
