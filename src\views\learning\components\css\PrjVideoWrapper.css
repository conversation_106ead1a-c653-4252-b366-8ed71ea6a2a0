.main-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.main-wrapper.big {
  justify-content: center;
  width: 100%;
  overflow: hidden;
}
.main-wrapper.big .main {
  width: 100%;
  max-width: 1380px;
  margin: 0 auto;
}
.main-wrapper.map .content-wrapper .content-card .video-container {
  display: none;
}
.main-wrapper.map .content-wrapper .content-card .map-container {
  display: block !important;
}
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  min-height: 0;
  overflow: hidden;
  margin: 0;
  padding: 10px;
  height: 100%;
}
.main .video-title {
  height: 25px;
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 10px;
}
.main .video-title .titlefont {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.main .video-title .klg-icon {
  cursor: pointer;
  position: relative;
  margin-right: 30px;
}
.main .video-title .klg-icon .klg-tooltip {
  position: absolute;
  transform: translateX(-50%);
  background: #666666;
  color: white;
  padding: 4px 12px;
  font-size: 12px;
  white-space: nowrap;
  border-radius: 4px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  pointer-events: none;
  margin-bottom: 5px;
  width: 70px;
  height: 24px;
  top: 100%;
  left: 35%;
}
.main .video-title .klg-icon:hover .klg-tooltip {
  opacity: 1;
  visibility: visible;
  z-index: 999;
}
.main .video-title .klg-icon .klg-icon-circle {
  width: 24px;
  height: 24px;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.main .video-title .klg-icon .klg-icon-circle:hover {
  background: #f2f2f2;
}
.main .video-title .klg-icon .klg-icon-circle .el-icon {
  font-size: 16px;
}
.main .content-wrapper {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  gap: 10px;
}
.main .content-wrapper .content-card {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  overflow: hidden;
  position: relative;
  padding: 20px;
  padding-bottom: 0;
}
.main .content-wrapper .content-card .video-container {
  aspect-ratio: 16/9;
  width: 100%;
  background-color: #000;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.main .content-wrapper .content-card .video-container :deep(.xg-player) {
  flex: 1;
  width: 100%;
  height: 100%;
  border-radius: 8px 8px 0 0;
}
.main .content-wrapper .content-card .video-control-bar {
  display: flex;
  align-items: center;
  padding: 5px 16px;
  background: #ffffff;
  height: 32px;
  margin-top: 20px;
}
.main .content-wrapper .content-card .video-control-bar .question-stats {
  display: flex;
  align-items: center;
  margin-right: 30px;
}
.main .content-wrapper .content-card .video-control-bar .question-stats .stats-text {
  color: #666666;
}
.main .content-wrapper .content-card .video-control-bar .script-toggle-button {
  width: 212px;
  background: #f2f2f2;
  color: #666666;
  border: none;
  box-sizing: border-box;
  border-radius: 5px;
  display: flex;
  text-align: center;
}
.main .content-wrapper .content-card .video-control-bar .script-toggle-button span {
  margin-left: 10px;
  margin-right: 75px;
}
.main .content-wrapper .content-card .video-control-bar .script-toggle-button:hover {
  cursor: pointer;
}
.main .content-wrapper .content-card .map-container {
  flex: 1;
  min-height: 0;
  display: none;
  position: relative;
  /* ksgMap覆盖层样式 - 参考PrjManuscript.vue */
}
.main .content-wrapper .content-card .map-container.show {
  display: block;
}
.main .content-wrapper .content-card .map-container .ksg-map-overlay {
  position: relative;
  width: 100%;
  height: calc(100vh - 130px);
  /* 保持与用户要求一致的高度 */
  z-index: 1000;
  border-radius: 5px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.main .content-wrapper .content-card .map-container .ksg-map-overlay :deep(.ksgmap) {
  height: 100% !important;
  width: 100% !important;
  /* 限制最大尺寸，保持良好的用户体验 */
  z-index: 1000;
}
.main .content-wrapper .content-card .map-container .ksg-map-overlay .close-icon {
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
  color: #666;
  z-index: 1001;
  transition: color 0.2s ease;
}
.main .content-wrapper .content-card .map-container .ksg-map-overlay .close-icon:hover {
  color: #409eff;
}
.main .content-wrapper .content-card .script-main-container {
  flex: 1;
  min-height: 0;
  background-color: #f8f9fa;
  overflow: hidden;
  border: 1px solid #f2f2f2;
  border-radius: 8px;
  padding-bottom: 0;
}
.main .content-wrapper .question-sidebar {
  width: 300px;
  min-width: 300px;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-top: 20px;
  box-sizing: border-box;
  padding-bottom: 10px;
}
.main .content-wrapper .question-sidebar .sidebar-header {
  cursor: pointer;
  padding: 8px;
  background-color: #f2f2f2;
  height: 35px;
  border-radius: 4px;
}
.main .content-wrapper .question-sidebar .sidebar-header .sidebar-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.main .content-wrapper .question-sidebar .sidebar-header .sidebar-title-wrapper .question-toggle {
  display: flex;
  align-items: center;
}
.main .content-wrapper .question-sidebar .sidebar-header .sidebar-title-wrapper .question-toggle .toggle-icon {
  color: #606266;
  font-size: 16px;
  transition: all 0.3s ease;
  padding: 4px;
  border-radius: 4px;
}
.main .content-wrapper .question-sidebar .sidebar-header .sidebar-title-wrapper .question-toggle .toggle-icon.expanded {
  transform: rotate(0deg);
}
.main .content-wrapper .question-sidebar .sidebar-header .sidebar-title-wrapper .question-toggle .toggle-icon:not(.expanded) {
  transform: rotate(0deg);
}
.main .content-wrapper .question-sidebar .question-list {
  flex: 1;
  overflow-y: auto;
  width: 100%;
  overflow-x: hidden;
  padding: 5px;
  transition: all 0.3s ease;
}
.main .content-wrapper .question-sidebar .question-list .question-item {
  padding: 8px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 30px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333333;
}
.main .content-wrapper .question-sidebar .question-list .question-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}
.main .content-wrapper .question-sidebar .question-list .question-item .question-time {
  width: 80px;
}
.main .content-wrapper .question-sidebar .question-list .question-item .description {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  width: 260px;
}
.main .content-wrapper .question-sidebar .question-list .question-item .description .key-words {
  text-align: center;
  max-width: 120px;
  max-height: 100%;
  line-height: 1;
}
.main .content-wrapper .question-sidebar .question-list .question-item .description .key-words img {
  height: 100%;
  width: 100%;
}
.main .content-wrapper .question-sidebar .question-list .question-item .description .question-type {
  display: inline-block;
  margin-left: 0;
}
.main .content-wrapper .question-sidebar .question-list .empty-state {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 20px;
  line-height: 1.6;
}
.pip-video-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 320px;
  height: 180px;
  z-index: 1000;
  overflow: hidden;
}
.pip-video-container .pip-video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}
.pip-video-container .pip-video-wrapper .pip-video-placeholder {
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 8px;
}
.pip-video-container .pip-video-wrapper .pip-video-placeholder .pip-video-content {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}
.pip-video-container .pip-video-wrapper .pip-video-placeholder .pip-video-content :deep(.xg-player) {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
.pip-video-container .pip-video-wrapper .pip-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1001;
}
.pip-video-container .pip-video-wrapper .pip-controls .el-button {
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
}
.pip-video-container .pip-video-wrapper .pip-controls .el-button:hover {
  background: rgba(0, 0, 0, 0.9);
}
.main-wrapper.script-mode .content-wrapper .content-card .video-container {
  display: none;
}
.main-wrapper.script-mode .content-wrapper .content-card .script-main-container {
  display: flex;
}
:deep(.divider) {
  margin: 0 !important;
}
.switchFloor {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 1000;
}
.switchFloor .btn {
  cursor: pointer;
  background-color: #dcdfe6;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-deep);
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.switchFloor .btn:hover,
.switchFloor .btn.light {
  background-color: var(--color-theme-project);
  color: white;
  transform: scale(1.1);
}
.switchFloor .btn .el-icon {
  font-size: 18px;
}
.main-wrapper.big .content-area {
  flex-direction: column;
}
.main-wrapper.big .content-area .video-container,
.main-wrapper.big .content-area .map-container {
  margin-right: 0;
  margin-bottom: 10px;
}
.main-wrapper.big .content-area .script-container {
  width: 100%;
  max-width: 1380px;
  margin: 0 auto;
  height: 400px;
}
.main-wrapper.map .content-area .video-container {
  display: none;
}
.main-wrapper.map .content-area .map-container {
  display: block;
}
.question-sidebar {
  position: relative;
}
.question-sidebar :deep(.question-drawer-wrapper) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}
.question-sidebar :deep(.answer-drawer-wrapper) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}
@media (max-width: 1200px) {
  .main {
    padding: 5px;
  }
  .content-area .script-container {
    width: 40%;
    min-width: 250px;
  }
}
@media (max-width: 768px) {
  .main-wrapper:not(.big) .content-area {
    flex-direction: column;
  }
  .main-wrapper:not(.big) .content-area .video-container,
  .main-wrapper:not(.big) .content-area .map-container {
    margin-right: 0;
    margin-bottom: 10px;
    height: 250px;
  }
  .main-wrapper:not(.big) .content-area .script-container {
    width: 100%;
    height: 300px;
  }
}
:deep(.paragraph-wrapper) {
  margin-bottom: 30px;
}
