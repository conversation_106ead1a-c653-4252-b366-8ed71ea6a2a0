//import { Interface } from 'readline';

// 习题类型
export enum ExerciseType {
  all = 0,
  single = 1,
  multi = 2,
  blank = 3,
  judge = 4
}
export interface contentType {
  optionId: string;
  text: string;
}
//todo:需要改
export interface exerciseItem {
  exerciseId: string; // 习题id
  stem: string; // 题干
  type: ExerciseType; // 类型
  content: contentType[]; //内容（只有选择题有）
  answer: string; // 答案
  explanation: string; // 解释说明
}
//todo:需要改
export interface answerContent {
  answer: string[]; // 答案
  explanation: string; // 解释说明
}
//获取问题列表用于显示高亮区域
export interface QuestionListData {
  /**
   * 关联文本内容
   */
  associatedWords?: string;
  /**
   * 问题ID
   */
  questionId: number;
}
