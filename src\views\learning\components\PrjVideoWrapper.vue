<template>
  <div class="main-wrapper" :class="{ map: isMap, 'script-mode': isScriptMode }">
    <!-- 主内容区 -->
    <div class="main">
      <!-- 标题栏 -->
      <div class="video-title">
        <div class="titlefont">
          {{ projectDetailData?.chapterName }}
        </div>
        <div class="klg-icon">
          <div class="klg-tooltip">知识源图</div>
          <span class="klgbtn klg-icon-circle" @click="toggleMap">
            <img src="@/assets/svgs/kGraph.svg" alt="" srcset="" />
          </span>
        </div>
      </div>
      <el-divider class="divider" />

      <!-- 主内容区域 - 对应 PrjManuWrapper 的内容结构 -->
      <div class="content-wrapper textfont" style="padding-top: 0 !important">
        <!-- 主内容卡片 - 对应 PrjManuWrapper 的 content-card -->
        <div class="content-card">
          <!-- 视频播放器区域 -->
          <div class="video-container" v-show="!isMap && !isScriptMode">
            <template v-if="projectDetailData?.videoUrl">
              <!-- <XgPlayer
                :videoSrc="projectDetailData?.videoUrl"
                :canBeWiden="prjType == PrjType.exam ? false : true"
                @timeupdate="handleTimeupdateFn"
                @wider="toggleDisplay"
                @closePip="switchScreen(0)"
                ref="player"
              /> -->
              <XgPlayer
                :videoSrc="projectDetailData?.videoUrl"
                :canBeWiden="prjType == PrjType.exam ? false : true"
                :questionTimelineMatches="questionTimelineMatches"
                @timeupdate="handleTimeupdateFn"
                @closePip="switchScreen(0)"
                ref="player"
              />
            </template>
          </div>
          <!-- 视频控制栏 -->
          <div class="video-control-bar" v-show="!isScriptMode && !isMap">
            <div class="question-stats">
              <span class="stats-text">共包含{{ questionList?.length || 0 }}个问题</span>
            </div>

            <el-button @click="toggleScriptMode" class="script-toggle-button textfont">
              <span>点这里划词提问</span>
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <!-- 知识地图区域 -->
          <div class="map-container" v-show="isMap">
            <!-- ksgMap覆盖层 -->
            <div class="ksg-map-overlay">
              <KsgMap
                ref="ksgRef"
                :config="config"
                :loading="loading"
                @load-more="handleLoadMore"
                @click-label="handleClickLabel"
                class="ksgmap"
              />
              <el-icon class="close-icon" size="2em" @click="closeMap">
                <Close />
              </el-icon>
            </div>
          </div>

          <!-- 视频脚本区域 - 在脚本模式下显示 -->
          <div class="script-main-container" v-show="isScriptMode && !isMap">
            <PrjVideoScript
              ref="prjVideoScript"
              :big="isBig ? true : false"
              :videoCaptionList="videoCaptionList"
              :renderedContent="renderedContent"
              :questionList="questionList"
              @returnInit="handleReturnInitFn"
              @refresh="handleQuestionList"
              @delete-question="handleDeleteQuestion"
              :isMap="isMap"
              v-if="ready"
              @wheel="handleVideoWheel"
              @search="handleSearch"
              @toggle-video="toggleScriptMode"
            />
          </div>
          <!-- 画中画视频窗口 -->
          <div class="pip-video-container" v-show="isScriptMode && !isMap">
            <div class="pip-video-wrapper">
              <!-- 画中画模式下显示主播放器的克隆 -->
              <div class="pip-video-placeholder">
                <div class="pip-video-content">
                  <!-- 这里会通过JavaScript将主播放器移动到此处 -->
                </div>
              </div>
              <div class="pip-controls">
                <el-button size="small" @click="toggleScriptMode"> 返回视频 </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧边栏 -  -->
        <div class="question-sidebar" v-show="!isBig">
          <div
            class="sidebar-header"
            @click="toggleQuestionList"
            :class="{ expanded: isQuestionListExpanded }"
          >
            <div class="sidebar-title-wrapper">
              <div class="sidebar-title">问题列表</div>
              <div class="question-toggle">
                <el-icon class="toggle-icon" :size="22">
                  <ArrowDown v-if="isQuestionListExpanded" />
                  <ArrowUp v-else />
                </el-icon>
              </div>
            </div>
          </div>
          <div class="question-list hover-scrollbar" v-show="isQuestionListExpanded">
            <div
              v-for="question in questionTimelineMatches"
              :key="question.questionId"
              class="question-item"
              @click="handleQuestionClick(question)"
            >
              <div
                class="question-time clickable-time"
                @click.stop="jumpToQuestionTime(question.startTime)"
                :title="'点击跳转到 ' + getDisplayTime(question.startTime)"
              >
                {{ getDisplayTime(question.startTime) }}
              </div>
              <span class="description">
                【
                <span class="key-words ellipsis textfont" v-html="question?.keyword || ''"></span>
                】
                <span v-if="question?.questionType != '开放性问题'" class="question-type textfont"
                  >{{ question?.questionType }}?</span
                >
              </span>
            </div>
            <div v-if="!questionList || questionList.length === 0" class="empty-state">
              暂无问题，开始划词提问吧！
            </div>
          </div>

          <!-- QuestionDrawer组件 -->
          <QuestionDrawer
            :visible="showQuestionDrawer"
            :selectedText="selectedText"
            :zIndex="componentZIndex.question"
            :buyStatus="props.buyStatus"
            @close="handleCloseQuestionDrawer"
            @submit="handleCloseQuestionDrawer"
            @showPayDialog="handleShowPayDialog"
          />

          <!-- AnswerDrawerSidebar组件 -->
          <AnswerDrawerSidebar
            :visible="showAnswerDrawer"
            :questionData="currentQuestionData"
            :projectAuthor="projectAuthor"
            :zIndex="componentZIndex.answer"
            @close="handleCloseAnswerDrawer"
            @show-question="handleShowQuestionFromFloating"
          />
        </div>
      </div>
    </div>

    <!-- 问题图标 -->
    <div
      v-if="questionIconVisible"
      ref="questionIconElement"
      class="question-icon"
      :style="{
        position: 'fixed',
        left: questionIconPosition.x + 'px',
        top: questionIconPosition.y + 'px',
        zIndex: 10000
      }"
      @click="handleQuestionIconClick"
    >
      <!-- 悬浮提示 -->
      <div class="question-tooltip">提问</div>
      <!-- 问号图标 -->
      <div class="question-icon-circle">
        <img :src="questionIcon" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  getPrjDetailApi,
  getPrjSectionApi,
  saveQuestionApi,
  getQuestionDetailApi
} from '@/apis/learning';
import type { Chapter, VideoCaptionListObj } from '@/types/learning';
import { STATE_FLAG } from '@/types/learning';
import XgPlayer from '@/components/XgPlayer.vue';
import { defineExpose } from 'vue';
import { getQuestionListApi, deleteQuestionApi } from '@/apis/learning';
import { ElMessage } from 'element-plus';
import PrjVideoScript from './PrjVideoScript.vue';
import ksg_window from './ksg_window.vue';
import { PrjType } from '@/types/project';
import userBehaviour from '@/utils/userBehaviour';
import { PrjForm } from '@/types/project';
import { useLearningStore } from '@/stores/learning';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { emitter } from '@/utils/emitter';
import { Close, ArrowDown, ArrowUp, ArrowRight } from '@element-plus/icons-vue';
import { getPrjIntroduceApi } from '@/apis/case';
import { useRenderManager } from '@/composables/useRenderManager';
import QuestionDrawer from '@/components/QuestionDrawer.vue';
import AnswerDrawerSidebar from '@/components/AnswerDrawerSidebar.vue';
import questionIcon from '@/assets/svgs/question.svg';
import { useQuestionIcon } from '@/composables/useQuestionIcon';
import { useDrawerManager } from '@/composables/useDrawerManager';
import {
  matchQuestionsWithTimeline,
  getDisplayTime,
  getSeconds,
  type QuestionTimelineMatch
} from '@/utils/Video';

// ksgMap相关导入
import { getAreaData, getFocusData, getChapterGraph, projectGraph } from '@/apis/ksgmap';
import { KsgMap } from '@endlessorigin/KsgMap';
import type {
  GlobalConfig,
  OriginalData,
  AreaData,
  PointData,
  FocusData
} from 'ksg-map/dist/types';
import { Event as EventType } from '@/types/event';
import type { QuestionType } from '@/types/question';

const router = useRouter();
const route = useRoute();
const learningStore = useLearningStore();
const drawerControllerStore = useDrawerControllerStore();
// const videoWordStore = useVideoWordStoreV2(); // 不再需要，使用Render对象替代
const { mode } = storeToRefs(drawerControllerStore);
const hasPermission = ref(0);

/**
 * 显示视频功能
 */
const isBig = ref(false); // 大屏小屏
const bigScreen = ref(0); // 0：大屏视频|1：大屏字幕
const isSectionMenuShow = ref(false); //控制多节菜单的显示
const player = ref<InstanceType<typeof XgPlayer> | null>(null);
const prjVideoScript = ref<InstanceType<typeof PrjVideoScript>>();
// 提供播放器实例给子组件
provide('player', player);
// if案例项目，小节号（if从开始学习|继续学习进来的then最新小节，else自选的节号）
const initChapterId = route.query.chapterId as string;
const spuId = route.query.spuId as string;
const prjType = inject('prjType') as Ref; // 1讲解 2案例 3测评
// 项目的prjId
const prjId = ref<string>('');
const uniqueCode = ref<string>('');
// 项目的章节id
const curChapterId = ref();
// 项目详情
const projectDetailData = ref<Chapter>();
// 章节相关的数据
const chapterList = ref<Chapter[]>();
const activeIndex = ref();
// 拿到章节信息
const videoCaptionList = ref<[VideoCaptionListObj[]]>([[]]);
const questionList = shallowRef<any[]>();
const playerTime = ref<number>(0);
const { initUserBehaviour, handleChapterChange } = userBehaviour(
  PrjForm.video,
  activeIndex,
  curChapterId,
  playerTime
);

const props = defineProps<{ payDialogVisible: boolean; buyStatus: boolean }>();

const emit = defineEmits<{
  'update:payDialogVisible': [value: boolean];
}>();

const payDialogVisible = ref(false);
watch(
  () => props.payDialogVisible,
  (newVal) => {
    if (newVal == false) {
      payDialogVisible.value = false;
    }
  }
);

// 提供isBig数据
provide(
  'isBig',
  computed(() => toValue(isBig))
);
// const isBig = inject('isBig') as Ref;
const isMap = ref(false); // 是否展示地图
const isScriptMode = ref(false); // 是否为脚本模式

// 使用问题图标管理 composable
const {
  questionIconVisible,
  questionIconPosition,
  currentSelectedText,
  showQuestionIcon,
  handleQuestionIconClick,
  handleDocumentClick,
  questionIconElement
} = useQuestionIcon();

// 使用Render管理器 - 针对视频字幕的特殊配置
const { reinitializeRender, handleSearch, addQuestion, removeQuestion } = useRenderManager({
  containerSelector: '#underline',
  getContentData: () => convertVideoCaptionListToTextArray(), // 直接返回字符串数组给render库处理
  questionList,
  onSelect: (data: any) => {
    // 当选中文本时，显示问号图标而不是直接打开弹窗
    if (data && data.content) {
      showQuestionIcon(data);
    }
  },
  onClick: (data: Event) => {
    const event = new CustomEvent('showAnswerDrawer', {
      detail: { questionData: data.target }
    });
    window.dispatchEvent(event);
  },
  onFinish: (arg: any) => {
    const content = arg.content;
    // console.log('myonFinish', content);
    // 将渲染后的内容存储到响应式变量中，传递给子组件
    renderedContent.value = content;
    console.log("renderedContent",renderedContent.value);
  }
  // enableDebugLog: true
});
const addQuestionFn = async (params: {
  associatedWords: string;
  keyword: string;
  questionType: QuestionType;
  questionDescription: string;
  uniqueCode: string;
  chapterId: string;
  contentId?: string;
}) => {
  const res = await saveQuestionApi(params);
  const data = res.data.data;
  if (res.success) {
    ElMessage.success('保存问题成功');
    console.log('保存的问题:', data);
    addQuestion(data.associatedWords, data.questionId);

    // 添加到本地问题列表
    const rawQuestionList = toRaw(questionList.value);
    if (rawQuestionList) {
      rawQuestionList.push(data);
      triggerRef(questionList);
    }

    // 重新匹配问题与视频文稿时间轴
    questionTimelineMatches.value = matchQuestionsWithTimeline(
      questionList.value || [],
      videoCaptionList.value
    );
  } else {
    const errorMsg = res.message || '保存问题失败，请重试';
    ElMessage.error(errorMsg);
  }
};

const removeQuestionFn = async ([questionId, associatedWords]: any) => {
  console.log('removeQuestionFn', questionId);
  const res = await deleteQuestionApi(questionId);
  // console.log("deleteQuestionApi",res.data)
  if (res.success) {
    ElMessage.success('删除成功');
    // 使用Render管理器处理问题删除
    removeQuestion(associatedWords, Number(questionId));

    // 从问题列表中移除
    const rawQuestionList = toRaw(questionList.value);
    const questionIndex = rawQuestionList?.findIndex(
      (item) => item.questionId === Number(questionId)
    );

    if (questionIndex !== undefined && questionIndex >= 0) {
      rawQuestionList?.splice(questionIndex, 1);
      triggerRef(questionList);
    }

    // 重新匹配问题与视频文稿时间轴
    questionTimelineMatches.value = matchQuestionsWithTimeline(
      questionList.value || [],
      videoCaptionList.value
    );
  } else {
    ElMessage.error('删除失败');
  }
};

// 使用抽屉管理 composable
const {
  showQuestionDrawer,
  showAnswerDrawer,
  selectedText,
  currentQuestionData,
  componentZIndex,
  componentStack,
  projectAuthor,
  updateComponentLayer,
  handleShowQuestionDrawer,
  handleShowAnswerDrawer,
  handleCloseQuestionDrawer,
  handleCloseAnswerDrawer: originalHandleCloseAnswerDrawer,
  handleShowQuestionFromFloating,
  initializeEventListeners,
  cleanupEventListeners
} = useDrawerManager();

const lastProcessedElement = ref<HTMLElement | null>(null); // 记录上次处理的DOM元素

// 问题列表展开/收起状态
const isQuestionListExpanded = ref(true);

// 问题与视频文稿匹配结果
const questionTimelineMatches = ref<QuestionTimelineMatch[]>([]);

// 渲染后的内容 - 用于传递给子组件
const renderedContent = ref<any>(null);

// 移除弹幕管理器，由XgPlayer组件内部处理

// 提供isMap数据
provide(
  'isMap',
  computed(() => toValue(isMap.value))
);

// 切换脚本模式
const toggleScriptMode = () => {
  isScriptMode.value = !isScriptMode.value;

  // 切换模式时隐藏问号图标和关闭弹窗
  questionIconVisible.value = false;
  showQuestionDrawer.value = false;
  showAnswerDrawer.value = false;
  currentSelectedText.value = '';
  selectedText.value = '';
  currentQuestionData.value = null;

  nextTick(async () => {
    if (isScriptMode.value) {
      // 进入脚本模式 - 将主播放器移动到画中画容器
      movePlayerToPip();

      // 等待DOM更新后初始化Render实例
      await nextTick();
      await reinitializeRender();

      console.log('🚀 进入脚本模式，初始化 Render 实例');
    } else {
      console.log('📺 退出脚本模式，移回播放器');
      movePlayerToMain();
    }
  });
};

// 将播放器移动到画中画容器
const movePlayerToPip = () => {
  const playerElement = player.value?.$el;
  const pipContainer = document.querySelector('.pip-video-content');

  if (playerElement && pipContainer) {
    // 保存原始父容器的引用
    const originalParent = playerElement.parentNode;
    if (originalParent) {
      originalParent.setAttribute('data-original-player', 'true');
    }

    // 移动播放器到画中画容器
    pipContainer.appendChild(playerElement);

    // 调整播放器尺寸以适应画中画容器
    playerElement.style.width = '100%';
    playerElement.style.height = '100%';
  }
};

// 将播放器移回主容器
const movePlayerToMain = () => {
  const playerElement = player.value?.$el;
  const originalParent = document.querySelector('[data-original-player="true"]');

  if (playerElement && originalParent) {
    // 移回原始容器
    originalParent.appendChild(playerElement);

    // 恢复原始样式
    playerElement.style.width = '';
    playerElement.style.height = '';

    // 清除标记
    originalParent.removeAttribute('data-original-player');
  }
};

// 处理问题列表中的问题点击（直接显示完整答案）
const handleQuestionClick = async (question: any) => {
  // 调用API获取问题详情
  const res = await getQuestionDetailApi(question.questionId.toString());

  if (res.success && res.data && res.data.length > 0) {
    // 强制触发props变化：先清空再设置，确保Vue能检测到变化
    currentQuestionData.value = null;

    // 使用nextTick确保Vue响应式系统处理了null值
    nextTick(() => {
      // 直接传递问题数据对象，跳过floating-content，直接显示完整答案
      currentQuestionData.value = res.data[0];
      showAnswerDrawer.value = true;

      // 更新组件层级
      updateComponentLayer('answer');
    });
  } else {
    console.error('❌ 获取问题详情失败:', res);
  }
};

// 处理显示PayDialog
const handleShowPayDialog = () => {
  console.log('到这里了');
  // 通过emit通知父组件打开PayDialog
  emit('update:payDialogVisible', true);
};

// 切换问题列表展开/收起状态
const toggleQuestionList = () => {
  isQuestionListExpanded.value = !isQuestionListExpanded.value;
};

// 问题图标相关逻辑已移至 useQuestionIcon composable

// 抽屉管理相关逻辑已移至 useDrawerManager composable
// 创建带有特定逻辑的关闭函数
const handleCloseAnswerDrawer = () => {
  originalHandleCloseAnswerDrawer();
  lastProcessedElement.value = null; // 重置处理记录
};

// 旧的函数已被useRenderManager替代

// 生成包含完整HTML结构的数据数组 - 保留时间戳信息用于句子级高亮
const convertVideoCaptionListToTextArray = () => {
  if (!videoCaptionList.value || !Array.isArray(videoCaptionList.value)) {
    return [];
  }

  const htmlArray: string[] = [];
  videoCaptionList.value.forEach((paragraphList, paragraphIndex) => {
    if (Array.isArray(paragraphList)) {
      // 构建包含时间戳信息的HTML结构
      let paragraphHtml = `<div class="paragraph-wrapper" data-paragraph="${paragraphIndex}">`;
      paragraphHtml += `<div class="text">`;

      // 为每个句子添加oid、data-start、data-end属性
      paragraphList.forEach((item) => {
        paragraphHtml += `<span oid="${item.oid}" data-start="${item.startTime}" data-end="${item.endTime}">${item.caption}</span>`;
      });

      paragraphHtml += `</div></div>`;

      if (paragraphHtml.trim()) {
        htmlArray.push(paragraphHtml);
      }
    }
  });
  console.log('convertVideoCaptionListToTextArray', htmlArray);
  return htmlArray;
};

/**
 * 点击问题时间跳转到对应的视频时间点
 * @param startTime 开始时间字符串（如 "00:01:30"）
 */
const jumpToQuestionTime = (startTime: string) => {
  if (startTime && player.value) {
    const seconds = getSeconds(startTime);
    player.value.setTime(seconds);
  }
};

// 监听问答组件状态变化，确保组件间状态同步
watch(
  () => [showQuestionDrawer.value, showAnswerDrawer.value],
  ([questionVisible, answerVisible]) => {
    // 当有弹窗显示时，通知其他组件
    const stateChangeEvent = new CustomEvent('drawerStateChange', {
      detail: {
        questionVisible,
        answerVisible,
        hasActiveDrawer: questionVisible || answerVisible
      }
    });
    window.dispatchEvent(stateChangeEvent);
  }
);
// const emits = defineEmits('closeMap');
// const toggleDisplay = () => {
//   if (!isBig.value) {
//     //isMap.value = false;
//     isBig.value = true;
//     //
//     // prjManuscript.value.changeStateFn(STATE_FLAG.init);
//   } else {
//     //isMap.value = true;
//     isBig.value = false;
//   }
// };
const switchScreen = (direction) => {
  if (bigScreen.value == direction) {
    return;
  }
  bigScreen.value = direction;
  if (direction == 0) {
    //isMap.value = true;
    player.value?.exitPIP();
  } else {
    isMap.value = false;
    player.value?.requestPIP();
  }
};

//================================ksg-map================================
// ksgMap相关变量
const ksgRef = ref<any>(null);
const target = ref<HTMLElement | null>(null);
const loading = ref<'loading' | 'loaded' | 'error'>('loading');
const config = ref<GlobalConfig>({
  fullScreen: false,
  showFullScreenButton: true,
  showArea: false,
  maxLevel: Infinity,
  targetArea: 'rootArea',
  allowFocus: false,
  autoLoadLevel: false
});

// 存储知识图谱数据的响应式变量
const ksgMapData = ref<any>(null);

// 标记是否已经获取过当前章节的数据
const hasLoadedChapterData = ref<string>('');

// 获取知识图谱数据的函数
const loadKsgMapData = async (chapterId: string) => {
  if (!chapterId || !spuId) return;

  try {
    const pointsData = await projectGraph(spuId, chapterId, 0, 10);

    // 将数据存储到响应式变量中
    ksgMapData.value = pointsData;
    hasLoadedChapterData.value = chapterId;

    return pointsData;
  } catch (error) {
    return null;
  }
};

// 处理加载更多事件
const handleLoadMore = async (params: any) => {
  // 这里可以根据需要实现加载更多逻辑
};

// 处理点击标签事件
const handleClickLabel = (label: any) => {
  // 这里可以根据需要实现点击标签逻辑
};

async function fetchAreaData(id: string): Promise<AreaData[]> {
  return await getAreaData(id);
}
async function fetchFocusData(id: string): Promise<FocusData> {
  return await getFocusData(id);
}

// 初始化知识图谱
async function init() {
  if (!ksgMapData.value) {
    return;
  }

  loading.value = 'loading'; // 设置加载状态

  try {
    // 使用与其他组件相同的方式初始化数据
    const dataList = (ksgMapData.value as any).records || ksgMapData.value;
    const total =
      (ksgMapData.value as any).total || (Array.isArray(dataList) ? dataList.length : 0);
    const rootid = '0';

    // 使用 firstLoadPointsData 方法初始化知识图谱
    ksgRef.value?.firstLoadPointsData(dataList, total, rootid);

    loading.value = 'loaded'; // 设置加载完成状态
  } catch (error) {
    loading.value = 'error'; // 设置错误状态
  }
}

// 关闭知识图谱
const closeMap = () => {
  isMap.value = false;
  if (player.value) {
    player.value.exitPIP();
  }
};

const saveType = inject('saveType') as Ref;

onMounted(async () => {
  saveType.value = 0;
  target.value = document.getElementById('app')!;
});

const toggleMap = async () => {
  isMap.value = !isMap.value;

  if (isMap.value) {
    // 显示知识图谱时，视频进入画中画模式
    player.value?.requestPIP();

    // 获取知识图谱数据并初始化
    const currentChapterId = String(curChapterId.value);

    if (!currentChapterId) {
      return;
    }

    // 如果当前章节没有数据，则获取数据
    if (!ksgMapData.value || hasLoadedChapterData.value !== currentChapterId) {
      await loadKsgMapData(currentChapterId);
    }

    // 如果有数据，则初始化知识图谱
    if (ksgMapData.value) {
      await init();
    }
  } else {
    // 隐藏知识图谱时，视频退出画中画模式
    player.value?.exitPIP();
  }
};
// 监听章节变化，清除知识图谱数据缓存
watch(
  () => curChapterId.value,
  (newChapterId) => {
    // 当章节变化时，清除之前的数据缓存
    if (newChapterId && String(newChapterId) !== hasLoadedChapterData.value) {
      ksgMapData.value = null;
      hasLoadedChapterData.value = '';
    }
  }
);

const handleQuestionList = async (uniqueCode: string, chapterId: string) => {
  const res = await getQuestionListApi(uniqueCode, chapterId);
  questionList.value = res.data;

  // 匹配问题与视频文稿时间轴
  questionTimelineMatches.value = matchQuestionsWithTimeline(
    questionList.value || [],
    videoCaptionList.value
  );
  console.log('questionTimelineMatches:', questionTimelineMatches.value);
};
const handleDeleteQuestion = (questionId: string) => {
  emitter.emit(EventType.REMOVE_QUESTION, questionId);
};

// TODO: 后期优化，增加节流
let scrollBarAble = true;
let scrollBarTimer: any;
const handleVideoWheel = () => {
  scrollBarAble = false;
  if (scrollBarTimer) {
    clearTimeout(scrollBarTimer);
  }
  scrollBarTimer = setTimeout(() => {
    scrollBarAble = true;
  }, 2000);
};
const tempOid = ref(-1);
const handleTimeupdateFn = (currentTime: number | string) => {
  // myPlayer的获取有问题，waq注释了这一句 todo需要修改
  // if (!myPlayer.value) return;
  playerTime.value = currentTime as number;
  // console.log('currentTime1111', currentTime)
  // FIXME: hoverPList没了，可能得换一个东西看
  // const hoverPList = prjManuscript.value?.hoverPList as VideoCaptionListObj[];
  // const hoverPList=videoCaptionList.value
  // console.log((e.target as HTMLVideoElement).currentTime.toFixed(2), typeof currentTime);
  // console.log(currentTime, typeof currentTime);
  // console.log('hoverPList', prjManuscript.value?.hoverPList);
  // console.count();
  let manuIndex = 0; //当前字幕段
  let idx = -1;
  let curoid = -1;
  manuIndex = videoCaptionList.value!.findIndex((hoverPList) => {
    idx = hoverPList?.findIndex((hoverParam) => {
      // console.log(hoverParam)
      if (
        getSeconds(hoverParam.startTime) <= currentTime &&
        getSeconds(hoverParam.endTime) >= currentTime
      ) {
        // console.log(hoverParam.caption)
        curoid = hoverParam.oid;
        return true;
      }
    });
    // console.log('idx', idx)
    if (idx != -1) return true;
  });
  if (idx != -1 && curoid != tempOid.value) {
    // console.log('idx',idx)
    // console.log('index',index)
    tempOid.value = curoid;
    prjVideoScript.value!.hoverPList(curoid); //字幕高亮
    if (scrollBarAble) {
      prjVideoScript.value!.scrollBar(manuIndex); // 滑动滚动条
    }
  }
};

provide(
  'prjSectionInfo',
  computed(() => ({
    chapterId: curChapterId.value,
    prjId: prjId.value,
    uniqueCode: uniqueCode.value
  }))
);

// 处理章节变化

//当我从一个章节切到另一个章节时，我需要发送现在章节的end请求和新章节的start请求, 结束现在章节的start请求和新章节的end请求

const handleChangeSectionFn = async (
  chapterId: number,
  preview: boolean,
  skipRouteUpdate = false
) => {
  if (!preview && !hasPermission.value) {
    /*learningStore.autoPay = true
    router.push({
      path: '/goodIntroduce',
      query: {
        spuId: spuId
      }
  })*/
    payDialogVisible.value = true;
  } else {
    if (activeIndex.value != chapterId) {
      // console.log('🔄 视频组件章节切换开始 - 从章节', activeIndex.value, '切换到章节', chapterId);

      // 🔥 关键修复：章节切换时强制退出脚本模式，确保以视频模式呈现
      if (isScriptMode.value) {
        console.log('🔄 章节切换时退出脚本模式，回到视频模式');
        isScriptMode.value = false;
        // 将播放器移回主容器
        movePlayerToMain();
      }

      // 🔥 关闭打开的抽屉组件
      showQuestionDrawer.value = false;
      showAnswerDrawer.value = false;
      selectedText.value = '';
      currentQuestionData.value = null;
      questionIconVisible.value = false;

      // 只有在不跳过路由更新时才更新路由
      if (!skipRouteUpdate) {
        router.replace({
          query: {
            ...route.query,
            chapterId: chapterId
          }
        });
      }
      handleChapterChange(chapterId);
      activeIndex.value = chapterId;

      // 获取新章节数据
      const res = await getPrjSectionApi(spuId, chapterId);
      console.log('📄 获取新章节数据:', res.data);

      // 更新章节相关数据
      projectDetailData.value = res.data;
      curChapterId.value = projectDetailData.value?.chapterId;
      videoCaptionList.value = res.data.videoCaptionList;
      console.log('📝 更新videoCaptionList:', videoCaptionList.value?.length, '条字幕');

      // 更新问题列表
      await handleQuestionList(uniqueCode.value, chapterId.toString());
      console.log('❓ 问题列表已更新');
    }
  }
};

// 小屏转大屏的时候操控状态STATE_FLAG
// 切换状态
const handleReturnInitFn = () => {
  prjVideoScript.value!.changeStateFn(STATE_FLAG.init);
};

const ready = ref(false);
onMounted(async () => {
  if (!learningStore.written) {
    const res = await getPrjDetailApi(spuId, initChapterId);
    console.log('getprjdetail:', res);
    learningStore.setInfo(res.data);
    learningStore.chapterId = learningStore.chapterList[0].chapterId;
    router.replace({
      query: {
        ...route.query,
        chapterId: learningStore.chapterId
      }
    });
  }
  // TODO:这里应该再接受一个type字段
  let idx = learningStore.validIndex; // 后端会把所有小节都返还，但只有一个对象是有信息的，就是这个index对应的小节
  projectDetailData.value = learningStore.chapterList[idx];
  //   initVideo(); // 初始化视频
  curChapterId.value = projectDetailData.value?.chapterId;

  chapterList.value = learningStore.chapterList; // 拿到章节列表
  activeIndex.value = learningStore.chapterList[idx]?.chapterId;
  //todo 接口修改需要调整,原来从接口读出来的是prjId
  //gc说改为prjId
  prjId.value = learningStore.prjId; // 拿到项目id
  uniqueCode.value = learningStore.uniqueCode;
  videoCaptionList.value = learningStore.chapterList[idx]?.videoCaptionList; // 拿到章节信息
  questionList.value = learningStore.chapterList[idx]?.questionList; // 拿到问题信息
  // uniqueCode.value = res.data.uniqueCode;
  // AsyncPrjManuscript = defineAsyncComponent(() => import('./PrjManuscript.vue'));
  initUserBehaviour(curChapterId.value);
  await handleQuestionList(uniqueCode.value, curChapterId.value as string);
  // 播放器事件监听由XgPlayer组件内部处理

  // showAsync.value = true;
  ready.value = true;
});
// onBeforeUpdate(() => {
//   console.count('父组件更新的次数');
//   console.log('父组件onBeforeUpdate');
// });

onMounted(() => {
  emitter.on(EventType.ADD_QUESTION, addQuestionFn);
  emitter.on(EventType.REMOVE_QUESTION, removeQuestionFn);
  document.addEventListener('click', handleDocumentClick);

  // 初始化抽屉管理事件监听
  initializeEventListeners();

  // 移除来自PrjVideoScript的事件监听，现在由本组件的Render对象直接处理
});

onBeforeUnmount(() => {
  // console.log('父组件onBeforeUnmount');
  // console.log('视频结束时长'+endTime.value.endTime)
  emitter.off(EventType.ADD_QUESTION, addQuestionFn);
  emitter.off(EventType.REMOVE_QUESTION, removeQuestionFn);
  document.removeEventListener('click', handleDocumentClick);

  // 清理抽屉管理事件监听
  cleanupEventListeners();
});

onMounted(async () => {
  // 优化：使用父组件提供的数据，避免重复调用API
  const projectIntroduceInfo = inject('projectIntroduceInfo', ref({}));
  const injectedHasPermission = inject('hasPermission', ref(0));
  const injectedProjectAuthor = inject('projectAuthor', ref(''));

  if (projectIntroduceInfo.value && Object.keys(projectIntroduceInfo.value).length > 0) {
    // 使用父组件已获取的数据
    hasPermission.value = injectedHasPermission.value;
    projectAuthor.value = injectedProjectAuthor.value;
  } else {
    const res = await getPrjIntroduceApi({
      spuId: route.query.spuId as string
    });
    hasPermission.value = res.data.hasPermission;
    projectAuthor.value = res.data.editorName || '';
  }
});

// 监听路由变化
watch(
  () => route.query.chapterId,
  async (newChapterId) => {
    if (newChapterId && Number(newChapterId) !== activeIndex.value) {
      console.log('视频组件路由章节变化:', newChapterId);
      await handleChangeSectionFn(Number(newChapterId), true, true); // 跳过路由更新，避免循环
    }
  }
);

defineExpose({
  curChapterId,
  handleChangeSectionFn
});
</script>

<style scoped src="./css/PrjVideoWrapper.css"></style>
