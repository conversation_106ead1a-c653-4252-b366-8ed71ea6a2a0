<template>
  <div class="content-section">
    <div class="card" style="display: flex; justify-content: space-between; max-width: 1190px; width: 100%; overflow: hidden">
<!--      <div class="left" style="flex: 1; background: #f2f2f2">-->
      <div class="left" style="flex: 1; max-width: 100%; overflow: hidden; min-width: 0; flex-shrink: 1">
        <div class="top-controls">
<!--          <div class="top-bar">-->
<!--            <span class="type-label">{{ mode === 1 ? '讲解' : (mode === 2 ? '测评' : '讲解和测评') }}</span>-->
<!--            <el-button class="ask-btn" type="info" size="small" plain @click="askDialogVisible = true" style="border: unset">-->
<!--              <el-icon style="margin-right: 4px; border: unset;"><QuestionFilled /></el-icon>-->
<!--              有看不懂的地方点我提问题-->
<!--            </el-button>-->
<!--          </div>-->
<!--          <div class="radio-group">-->
<!--            <el-radio-group v-model="mode" @change="handleModeChange(mode)">-->
<!--              <el-radio :label="1">边学边测</el-radio>-->
<!--              <el-radio :label="2">只看讲解</el-radio>-->
<!--              <el-radio :label="3">只做测评</el-radio>-->
<!--            </el-radio-group>-->
<!--          </div>-->
<!--          <div class="header-arrows">-->
<!--            <div class="label-wrapper" style="margin-right: 15px">-->
<!--              <el-icon :class="{ disabled: isFirstItem }" @click="changeProject('left')">-->
<!--                <ArrowLeft />-->
<!--              </el-icon>-->
<!--              <span-->
<!--                class="nav-label"-->
<!--                :class="{ disabled: isFirstItem }"-->
<!--                @click="changeProject('left')"-->
<!--                >上一个</span-->
<!--              >-->
<!--            </div>-->
<!--            <div class="label-wrapper">-->
<!--              <span-->
<!--                class="nav-label"-->
<!--                :class="{ disabled: isLastItem }"-->
<!--                @click="changeProject('right')"-->
<!--                >下一个</span-->
<!--              >-->
<!--              <el-icon :class="{ disabled: isLastItem }" @click="changeProject('right')">-->
<!--                <ArrowRight />-->
<!--              </el-icon>-->
<!--            </div>-->
<!--          </div>-->
          
        </div>

        <template v-if="ready">
          <template v-if="isPrj">
            <DraftWrapper
              v-if="prjForm == PrjForm.draft && ready"
              :prjInfo="prjInfo"
              :targetKlgs="targetKlgs"
              :prjTargetObj="prjTargetObj"
              :wordContent="draftWordContent"
              :transmitUniqueCode="transmitUniqueCode"
              :transmitSpuId="transmitSpuId"
              :transmitChapterId="transmitChapterId"
            />
            <VideoWrapper
              v-else-if="prjForm == PrjForm.video && ready"
              @move="mve"
              :prjInfo="prjInfo"
              :targetKlgs="targetKlgs"
              :prjTargetObj="prjTargetObj"
              :videoUrl="videoUrl"
              :transmitChapterId="transmitChapterId"
              :transmitUniqueCode="transmitUniqueCode"
              :transmitSpuId="transmitSpuId"
          /></template>
          <template v-else>
            <!-- <DraftExam v-if="showType == ShowType.draft" :state="state"></DraftExam> -->
            <TestWrapper
              v-if="!isPrj"
              :exerciseInfo="exerciseInfo"
              :isLastItem="isLastItem"
              :klgCode="klgCode"
              :transmitUniqueCode="transmitUniqueCode"
              :transmitSpuId="transmitSpuId"
              :transmitChapterId="transmitChapterId"
              @toNext="changeProject('right')"
            ></TestWrapper>
          </template>
        </template>
      </div>

      <div class="right-panel">
        <!-- 顶部模式选择 -->
        <div class="mode-select-simple">
          <div
            style="width: 29px; height: 29px; border-bottom-left-radius: unset; border-bottom-right-radius: unset"
            :class="{selected: mode === 1 || mode === 3}"
            @click="toggleMode(1)"
          >
            <el-icon style="width: 29px; height: 29px;"><img src="@/assets/svgs/book.svg" style="width: 1.2rem; height: 1.2rem;"/></el-icon>
          </div>
          <div
            style="width: 29px; height: 29px; border-top-left-radius: unset; border-top-right-radius: unset"
            :class="{selected: mode === 2 || mode === 3}"
            @click="toggleMode(2)"
          >
            <el-icon style="width: 29px; height: 29px;"><img src="@/assets/svgs/pen.svg" style="width: 1.2rem; height: 1.2rem;"/></el-icon>
          </div>
        </div>
        <!-- 中间上下按钮 -->
        <div class="arrow-buttons">
          <div class="arrow-btn-wrapper">
            <el-icon class="arrow-btn" @click="changeProject('left')"><img src="@/assets/svgs/up.svg" style="width: 1rem; height: 1rem;"/></el-icon>
          </div>
          <div class="arrow-btn-wrapper">
            <el-icon class="arrow-btn" @click="changeProject('right')"><img src="@/assets/svgs/down.svg" style="width: 1rem; height: 1rem;"/></el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

//6/26
import VideoWrapper from '@/views/klgdetail/components/VideoWrapper.vue';
import TestWrapper from '@/views/klgdetail/components/TestWrapper.vue';
import { defineProps, computed } from 'vue';

const props = defineProps<{
  recommends: string[],
}>();


import { PrjType } from '@/types/project';
import { PrjForm } from '@/types/project';
import type { PrjinfoItf } from '@/types/learning';
import type { ExerciseType, exerciseItem } from '@/types/exercise';
import DraftWrapper from '@/views/klgdetail/components/DraftWrapper.vue';
//import PrjManuWrapper from '@/views/klgdetail/PrjManuWrapper.vue';
//import PrjVideoWrapperNew from '@/views/klgdetail/PrjVideoWrapperNew.vue';
import DraftExam from '@/views/exam/components/DraftExam.vue';
import ExerciseWrapper from '@/views/klgdetail/ExerciseWrapper.vue';
import anime from 'animejs/lib/anime.es.js';
import { useDrawerControllerStore } from '@/stores/drawerController';
import { useLearningStore } from '@/stores/learning';
import {
  getPartProApi,
  getPrjDetailApi,
  getPrjMoreInfoApi,
  getIdListApi,
  getExerciseDetailApi,
  getPrjSectionApi
} from '@/apis/learning';
import { getExerciseApi } from '@/apis/exercise';
import { ArrowLeft, ArrowRight, ArrowUp, ArrowDown, CopyDocument, EditPen, Collection, QuestionFilled } from '@element-plus/icons-vue';
import { Console } from 'console';
import { useDraftWordStoreV2 } from '@/stores/draftWordV2';
import { ElMessage } from 'element-plus';
import AskQuestionDialog from './AskQuestionDialog.vue';
import { getKlgRecommend } from '@/apis/klgdetail';
import { getPrjIntroduceApi } from '@/apis/case';

const ready = ref(false);
const spuId = ref('0');
const exerciseId = ref('0');
const chapterId = ref('0');
const videoUrl = ref('');
const learningStore = useLearningStore();

const prjInfo = ref<PrjinfoItf>({});
const prjForm = ref<PrjForm>();
const prjType = ref<PrjType>();
const exerciseInfo = ref<exerciseItem>({
  exerciseId: '',
  stem: '',
  explanation: '',
  content: [],
  answer: '',
  type: 0
});

const ids = ref<string[]>([]); // 用于存储获取到的所有项目ID
const currentIndex = ref(0); // 当前选中的项目索引
const currentPage = ref(1); // 当前页码
const isFirstItem = computed(() => currentIndex.value == 0); // 判断是否是第一个项目
const isLastItem = computed(() => currentIndex.value == ids.value.length - 1); // 判断是否是最后一个项目

// 新增：项目数据缓存（固定大小，FIFO策略）
const CACHE_SIZE = 10; // 缓存大小限制
const projectCache = ref<Map<string, any>>(new Map()); // 缓存项目数据
const exerciseCache = ref<Map<string, any>>(new Map()); // 缓存测评数据

// 缓存管理函数
function addToCache(cache: Map<string, any>, key: string, value: any) {
  // 如果缓存已满，删除最早的项目
  if (cache.size >= CACHE_SIZE) {
    const firstKey = cache.keys().next().value;
    if (firstKey) {
      cache.delete(firstKey);
      console.log('缓存已满，删除最早的项目:', firstKey);
    }
  }
  cache.set(key, value);
}

function getFromCache(cache: Map<string, any>, key: string) {
  return cache.get(key);
}

//模式单选框
const mode = ref(3); // 1:只选上，2:只选下，3:都选
function toggleMode(val: number) {
  if (mode.value === 3) {
    // 当前都选，点击某个就只选另一个
    handleModeChange(val === 1 ? 2 : 1);
  } else if (mode.value === val) {
    ElMessage.info('至少保留一个模式');
  } else {
    handleModeChange(3);
  }
}

const handleModeChange = async (value: number) => {
  const oldMode = mode.value;
  // 1. 判断当前项目是否符合新模式
  const curId = ids.value[currentIndex.value];
  const isCurPrj = !/^[\d]+$/.test(curId);
  let match = false;
  if (value === 1 && isCurPrj) match = true;
  if (value === 2 && !isCurPrj) match = true;
  if (value === 3) match = true;

  let targetId = curId;
  let targetIdx = currentIndex.value;
  let found = match;
  let page = 1;
  let allIds = [...ids.value];

  // 2. 如果不符合，向后查找下一个符合条件的 id
  if (!match) {
    // 先在已有ids里查找
    for (let i = currentIndex.value + 1; i < allIds.length; i++) {
      const id = allIds[i];
      if (value === 1 && !/^[\d]+$/.test(id)) {
        targetId = id;
        targetIdx = i;
        found = true;
        break;
      }
      if (value === 2 && /^[\d]+$/.test(id)) {
        targetId = id;
        targetIdx = i;
        found = true;
        break;
      }
      if (value === 3) {
        targetId = id;
        targetIdx = i;
        found = true;
        break;
      }
    }
    // 如果没找到，继续用getKlgRecommend加载新数据
    page = currentPage.value + 1; // 当前页码加1
    while (!found) {
      console.log("第一步没找到")
      //2025-07-04修改，解决可能出现的bug
      //const typeParam = value === 3 ? 0 : value;
      const typeParam = oldMode === 3 ? 0 : value;
      const resList = await getKlgRecommend(klgCode, page, typeParam.toString());
      let newIds = resList.data;
      // 兼容后端返回格式
      if (newIds && newIds.recommend && Array.isArray(newIds.recommend.List)) {
        newIds = newIds.recommend.List;
      }
      if (!newIds || newIds.length === 0) break;
      for (let i = 0; i < newIds.length; i++) {
        const id = newIds[i];
        allIds.push(id);
        if (value === 1 && !/^[\d]+$/.test(id)) {
          targetId = id;
          targetIdx = allIds.length - 1;
          found = true;
          break;
        }
        if (value === 2 && /^[\d]+$/.test(id)) {
          targetId = id;
          targetIdx = allIds.length - 1;
          found = true;
          break;
        }
        if (value === 3) {
          targetId = id;
          targetIdx = allIds.length - 1;
          found = true;
          break;
        }
      }
      if (found) break;
      page++;
    }
  }

  if (found) {
    // 重新拼接 ids，保证 ids 只包含新模式下的顺序
    let allIdsNew: string[] = [];
    let pageNew = 1;
    let foundInPage = false;
    const typeParam = value === 3 ? 0 : value;
    while (!foundInPage) {
      const resList = await getKlgRecommend(klgCode, pageNew, typeParam.toString());
      let pageIds = resList.data;
      // 兼容后端返回格式
      if (pageIds && pageIds.recommend && Array.isArray(pageIds.recommend.List)) {
        pageIds = pageIds.recommend.List;
      }
      if (!pageIds || pageIds.length === 0) break;
      allIdsNew = allIdsNew.concat(pageIds);
      if (pageIds.includes(targetId)) {
        foundInPage = true;
        break;
      }
      pageNew++;
    }
    if (foundInPage) {
      ids.value = allIdsNew;
      currentIndex.value = allIdsNew.indexOf(targetId);
      mode.value = value;
      //2025-07-04新加，用于解决bug
      currentPage.value = pageNew;

      console.log("更新后的ids数组")
      console.log(ids.value)
    } else {
      ElMessage.info('没有符合条件的项目，模式不变');
      mode.value = oldMode
    }

    const id = ids.value[currentIndex.value];
    separateIds(id);

    if (isPrj.value == true) {
      //讲解项目
      // console.log('prj!!!', isPrj);
      exerciseId.value = '';
      spuId.value = id;
      await handlePrj(spuId.value);
    } else {
      //测评
      // console.log('exercise!!!', isPrj);
      spuId.value = '';
      exerciseId.value = id;
      await handleExercise(exerciseId.value);
    }

  } else {
    ElMessage.info('没有符合条件的项目，模式不变');
    mode.value = oldMode;
  }
};

const tomove = ref(false);

// const descriptionVisible = ref(false);
// const tagsVisible = ref(false);
const drawerControllerStore = useDrawerControllerStore();

const router = useRouter();
const route = useRoute();
const klgCode = inject('klgCode') as string;

provide('prjInfo', prjInfo);
provide('prjForm', prjForm);
provide('prjType', prjType);

// provide('descriptionVisible', descriptionVisible.value);
// provide('tagsVisible', tagsVisible.value);

const useWideScreen = inject('useWideScreen') as Ref<boolean>;

const prjTargetObj = ref<{
  description: string;
  purpose: string;
}>({ description: '', purpose: '' }); // 项目描述和项目目标

const klg = ref<{
  klgNumbers: number;
  learned: number;
  graspKlg: number;
}>({ klgNumbers: 0, learned: 0, graspKlg: 0 });
provide('klg', klg);

const targetKlgs = ref<
  [
    {
      klgCode: string;
      klgTitle: string;
      choose: boolean;
    }
  ]
>([{ klgCode: '', klgTitle: '', choose: false }]); // 目标知识点

async function mve(mode: boolean) {
  if (mode) {
    tomove.value = true;
  } else {
    tomove.value = false;
  }
}
// const props = defineProps({
//   message: {
//     type: String,
//     required: true
//   },
//   user: {
//     type: Object,
//     default: () => ({})
//   }
// })


//做判断区分项目、测评
const isPrj = ref<boolean>(true);
function separateIds(id: string) {
  if (/^\d+$/.test(id)) {
    isPrj.value = false; //测评
  } else if (/^P\d+$/.test(id)) {
    isPrj.value = true; //讲解
  }
}

const transmitSpuId = ref("");
const transmitChapterId = ref("");
const transmitUniqueCode = ref("");

const draftWordContent = ref('');

async function handlePrj(spuId: string) {
  // 检查缓存
  const cachedData = getFromCache(projectCache.value, spuId);
  if (cachedData) {
    console.log('使用缓存的项目数据:', spuId);

    // 从缓存恢复数据
    draftWordContent.value = cachedData.draftWordContent;
    videoUrl.value = cachedData.videoUrl;
    prjInfo.value = cachedData.prjInfo;
    prjForm.value = cachedData.prjForm;
    prjType.value = cachedData.prjType;
    prjTargetObj.value = cachedData.prjTargetObj;
    klg.value = cachedData.klg;
    targetKlgs.value = cachedData.targetKlgs;
    transmitSpuId.value = cachedData.transmitSpuId;
    transmitChapterId.value = cachedData.transmitChapterId;
    transmitUniqueCode.value = cachedData.transmitUniqueCode;
    chapterId.value = cachedData.chapterId;
    learningStore.chapterList = cachedData.chapterList;
    learningStore.chapterId = cachedData.chapterId;

    // 恢复路由
    router.replace({
      query: {
        ...route.query,
        chapterId: chapterId.value,
        spuId: spuId
      }
    });
    return;
  }

  // 缓存未命中，获取新数据
  console.log('获取新的项目数据:', spuId);

  // 先清空，防止闪现旧内容
  draftWordContent.value = '';
  videoUrl.value = '';
  
  //ready.value = false;
  const chapterIdRes = await getPrjIntroduceApi({ spuId: spuId })
  const res = await getPartProApi(spuId);
  learningStore.setInfo(res.data);
  //let idx = learningStore.validIndex;
  learningStore.chapterList = chapterIdRes.data.chapterList;
  learningStore.chapterId = learningStore.chapterList[0].chapterId;


  //换一个位置
  // 获取视频URL
  // if (res.data.list[0].prjForm === PrjForm.video) {
  //   const sectionRes = await getPrjSectionApi(spuId, learningStore.chapterId);
  //   videoUrl.value = sectionRes.data.videoUrl;
  // }
  
  //6/26晚上新加
  const ds = await getPrjDetailApi(spuId, learningStore.chapterId);
  transmitSpuId.value = spuId;
  transmitChapterId.value = learningStore.chapterList[0].chapterId;
  transmitUniqueCode.value = ds.data.uniqueCode;

  // 新增：存储文稿内容
  draftWordContent.value = ds.data.chapterList[0].wordContent || '';

  //7/1新加
  prjInfo.value.uniqueCode = ds.data.uniqueCode;
  //localStorage.setItem("uniqueCode", ds.data.uniqueCode)
  //localStorage.setItem("spuId", spuId)
  //localStorage.setItem("chapterId", learningStore.chapterList[0].chapterId)




  chapterId.value = chapterIdRes.data.chapterList[0].chapterId;
  //获取知识讲解（视频/文稿）基本信息
  //const res1 = await getPartProApi(spuId);
  const res1 = res;
  prjInfo.value = res1.data.list[0];
  prjForm.value = prjInfo.value.prjForm;
  prjType.value = prjInfo.value.prjType;
  const res2 = await getPrjMoreInfoApi(spuId); //获取小header
  prjTargetObj.value = res2.data.prj;
  klg.value = res2.data.klg;
  targetKlgs.value = res2.data.targetKlgs;


  //换到这里来
  if (res.data.list[0].prjForm === PrjForm.video) {
    const sectionRes = await getPrjSectionApi(spuId, learningStore.chapterId);
    videoUrl.value = sectionRes.data.videoUrl;
  }

  // 缓存数据
  addToCache(projectCache.value, spuId, {
    draftWordContent: draftWordContent.value,
    videoUrl: videoUrl.value,
    prjInfo: { ...prjInfo.value },
    prjForm: prjForm.value,
    prjType: prjType.value,
    prjTargetObj: { ...prjTargetObj.value },
    klg: { ...klg.value },
    targetKlgs: [...targetKlgs.value],
    transmitSpuId: transmitSpuId.value,
    transmitChapterId: transmitChapterId.value,
    transmitUniqueCode: transmitUniqueCode.value,
    chapterId: chapterId.value,
    chapterList: [...learningStore.chapterList]
  });

  //讲解类有章节列表
  //console.log('learning111', learningStore.chapterList[0].videoCaptionList[0][0].caption);
  router.replace({
    query: {
      ...route.query,
      chapterId: chapterId.value,
      spuId: spuId
    }
  });
  //在这里等待一秒，防止他加载不出来
  //ready.value = true;
}

async function handleExercise(exerciseId: string) {
  // 检查缓存
  const cachedData = getFromCache(exerciseCache.value, exerciseId);
  if (cachedData) {
    console.log('使用缓存的测评数据:', exerciseId);

    // 从缓存恢复数据
    exerciseInfo.value = cachedData.exerciseInfo;
    transmitSpuId.value = cachedData.transmitSpuId;
    return;
  }

  // 缓存未命中，获取新数据
  console.log('获取新的测评数据:', exerciseId);

  //用于弹窗的判断条件
  exerciseInfo.value.exerciseId = '';
  //transmitSpuId.value = spuId;
  const res = await getExerciseApi(exerciseId);
  exerciseInfo.value = res.data;

  // 缓存数据
  addToCache(exerciseCache.value, exerciseId, {
    exerciseInfo: { ...exerciseInfo.value },
    transmitSpuId: transmitSpuId.value
  });
}

// 异步预加载项目详细数据
async function preloadNextBatch(idsToPreload: string[]) {
  console.log('开始预加载项目数据:', idsToPreload);
  
  for (const id of idsToPreload) {
    try {
      if (/^P\d+$/.test(id)) {
        // 预加载项目数据
        if (!projectCache.value.has(id)) {
          console.log('预加载项目数据:', id);
          const chapterIdRes = await getPrjIntroduceApi({ spuId: id });
          const res = await getPartProApi(id);
          const ds = await getPrjDetailApi(id, chapterIdRes.data.chapterList[0].chapterId);
          const res2 = await getPrjMoreInfoApi(id);
          
          addToCache(projectCache.value, id, {
            draftWordContent: ds.data.chapterList[0].wordContent || '',
            videoUrl: res.data.list[0].prjForm === PrjForm.video ? 
              (await getPrjSectionApi(id, chapterIdRes.data.chapterList[0].chapterId)).data.videoUrl : '',
            prjInfo: res.data.list[0],
            prjForm: res.data.list[0].prjForm,
            prjType: res.data.list[0].prjType,
            prjTargetObj: res2.data.prj,
            klg: res2.data.klg,
            targetKlgs: res2.data.targetKlgs,
            transmitSpuId: id,
            transmitChapterId: chapterIdRes.data.chapterList[0].chapterId,
            transmitUniqueCode: ds.data.uniqueCode,
            chapterId: chapterIdRes.data.chapterList[0].chapterId,
            chapterList: chapterIdRes.data.chapterList
          });
        }
      } else if (/^\d+$/.test(id)) {
        // 预加载测评数据
        if (!exerciseCache.value.has(id)) {
          console.log('预加载测评数据:', id);
          const res = await getExerciseApi(id);
          addToCache(exerciseCache.value, id, {
            exerciseInfo: res.data,
            transmitSpuId: spuId.value
          });
        }
      }
    } catch (error) {
      console.error('预加载项目详情失败:', id, error);
    }
  }
}

async function loadProjects(tagId: number) {
  //console.log('loadProjects called, tagId:', tagId, 'currentPage:', currentPage.value);
  //const resList = await getIdListApi(klgCode, tagId); // 获取项目ID列表

  //当前类型
  //当mode = 1:只选上，type = 1
  // 当mode = 2:只选下，type = 2
  // 当mode = 3:都选,type = 0
  let type = 0;
  if (tagId === 1) {
    type = 1;
  } else if (tagId === 2) {
    type = 2;
  } else if (tagId === 3) {
    type = 0;
  }
  
  //const resList = await getKlgRecommend(klgCode, 1, 0)
  const resList = await getKlgRecommend(klgCode, currentPage.value, type.toString())
  //下面这一行不要修复，就是这样的，不要管它的报错
  const newIds = resList.data;
  //console.log('API返回数据:', resList.data, 'newIds:', newIds);

  if (newIds && newIds.length > 0) {
    // 拼接新数据
    const oldLen = ids.value.length;
    ids.value = ids.value.concat(newIds);
    currentIndex.value = oldLen; // 指向新页的第一个题目
    //console.log('拼接数据，currentIndex:', currentIndex.value);
    const id = ids.value[currentIndex.value];
    separateIds(id);
    
    // 立即加载第一个项目，不等待
    if (isPrj.value == true) {
      exerciseId.value = '';
      spuId.value = id;
      handlePrj(spuId.value);
    } else {
      spuId.value = '';
      exerciseId.value = id;
      handleExercise(exerciseId.value);
    }
    
    //异步预加载后续项目的详细数据（不影响第一个项目的展示）
    setTimeout(() => {
      if (Array.isArray(newIds) && newIds.length > 1) {
        preloadNextBatch(newIds.slice(1)); // 从第二个项目开始预加载
      }
    }, 100); // 延迟100ms，确保第一个项目优先加载


  } else {
    // 没有新数据，回退页码并提示
    //console.log('没有数据，回退页码');
    if (currentPage.value > 1) {
      currentPage.value--;
      ElMessage.info('已经是最后一页了');
      //console.log('回退到页码:', currentPage.value);
    } else {
      ElMessage.info('暂无数据');
      //console.log('第一页也没有数据');
    }
  }
}
watch(
  () => spuId.value,
  (newV) => {
    //console.log('index:', newV);
  }
);

function changeProject(direction: 'left' | 'right') {
  ready.value = false;
  changeProject1(direction);
  ready.value = true;
}
async function changeProject1(direction: 'left' | 'right') {
  //ready.value = false;
  //console.log('changeProject called:', direction, 'currentIndex:', currentIndex.value, 'ids.length:', ids.value.length);
  
  if (direction == 'left' && currentIndex.value > 0) {
    currentIndex.value--;
    //console.log('向左导航，新索引:', currentIndex.value);
  } else if (direction == 'left' && currentIndex.value === 0) {
    ElMessage.info('已经是第一页了');
    return;
  } else if (direction == 'right' && currentIndex.value < ids.value.length - 1) {
    currentIndex.value++;
    //console.log('向右导航，新索引:', currentIndex.value);
  } else if (direction == 'right' && currentIndex.value >= ids.value.length - 1) {
    // 当前页已经看完，尝试加载下一页
    //console.log('尝试加载下一页，当前页:', currentPage.value);
    currentPage.value++;

    //防止加载不出来
    ready.value = false;
    await loadProjects(mode.value);
    ready.value = true;




    return; // 让loadProjects处理后续逻辑
  } else {
    //console.log('无效的导航操作');
    return;
  }
  
  //console.log('处理项目切换，currentIndex:', currentIndex.value);
  const id = ids.value[currentIndex.value];
  separateIds(id);

  if (isPrj.value == true) {
    //讲解项目
    // console.log('prj!!!', isPrj);
    exerciseId.value = '';
    spuId.value = id;
    await handlePrj(spuId.value);
  } else {
    //测评
    // console.log('exercise!!!', isPrj);
    spuId.value = '';
    exerciseId.value = id;
    await handleExercise(exerciseId.value);
  }
  //ready.value = true;
}

onMounted(() => {
  ready.value = false;
  loadProjects(mode.value);

  ready.value = true;
  

  //ready.value = true;
  useWideScreen.value = false;
});

onBeforeUnmount(() => {
  useWideScreen.value = false;
});

// const header = ref();

// function handleClose(event: MouseEvent) {
//   if (header.value.contains(event.target as HTMLElement)) {
//     return;
//   }
//   descriptionVisible.value = false;
//   tagsVisible.value = false;
// }

// onMounted(() => {
//   document.addEventListener('click', handleClose);
//   watch(
//     () => descriptionVisible.value || tagsVisible.value,
//     () => {
//       if (descriptionVisible.value || tagsVisible.value) {
//         anime({
//           targets: header.value,
//           backgroundColor: {
//             value: '#F2F2F2',
//             duration: 300,
//             easing: 'linear'
//           }
//         });
//       } else {
//         anime({
//           targets: header.value,
//           backgroundColor: {
//             value: '#FFFFFF',
//             duration: 300,
//             easing: 'linear'
//           }
//         });
//       }
//     }
//   );
// });
// onBeforeUnmount(() => {
//   document.removeEventListener('click', handleClose);
// });
</script>

<style scoped>
.content-section {
  padding: 10px 0 10px 10px;
  .card {
    /* width: var(--width-fixed--project); */
    width: 100%;
    //height: 620px;
    min-height: 620px;
    /* padding-left: var(--padding-box);
    padding-right: var(--padding-box);
    padding-top: 10px; */
    margin: 0 auto;
    //background-color: #f2f2f2;
    .left {
      max-width: 100%;
      overflow: hidden;
      min-width: 0;
      flex-shrink: 1;
      .top-controls {
        display: flex;
        background-color: #ffffff;
      }
      .radio-group {
        background-color: #ffffff;
      }
      .left-header {
        background-color: #f2f2f2;
        margin-left: 10px;
        width: 668px;
        /* width: calc(61.8vw - 10px); */
        &.big {
          margin: 0 auto;
        }
        .title {
          font-size: 18px;
          font-weight: 700;
          margin: 10px 0px 10px 0;
        }
        .base-info {
          font-size: 12px;
          display: flex;
          align-items: center;
          color: var(--color-deep);
          .creater {
            display: flex;
            align-items: center;
            .avatar {
              width: 15px;
              height: 15px;
              border-radius: 50%;
              margin-right: 5px;
            }
            .name {
            }
          }
          .time {
            margin: 0 10px;
          }
          .function-tag {
            margin: 0 10px;
            padding: 0 10px;
            border-radius: 10px;
            border: 1px solid var(--color-deep);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: break-all;

            &:hover {
              background-color: var(--color-inactive-project);
              cursor: pointer;
            }
          }
        }
      }

      .left-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        width: calc(61.8vw - 10px);
        display: flex;
        &.big {
          height: calc(100vh - 160px);
        }

        .video-title {
          padding-left: 10px;
          padding-right: 10px;
          position: relative;
          bottom: 0px;
          background-color: #f2f2f2;
          height: 63px;
          font-size: 24px;
          font-weight: 400;
          color: var(--color-black);
          font-family: var(--title-family);
          display: flex;
          justify-content: space-between;
          align-items: center;
          .btn {
            background-color: white;
            border-radius: 14px;
            height: 28px;
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 10px;
            border: 1px solid var(--color-theme-project);
            color: var(--color-theme-project);
            font-family: var(--text-family);
            &:hover {
              background-color: var(--color-theme-project);
              color: white;
              cursor: pointer;
            }
          }
          .icon {
            cursor: pointer;
          }

          .icon-wrapper {
            width: 16px;
            height: 12px;
            margin: 0 5px;
            background: url('@/assets/svgs/u2985.svg') no-repeat center/contain;
            cursor: pointer;

            &:hover {
              background-image: url('@/assets/svgs/u4176.svg');
            }
          }
        }

        .video {
          /* background-color: rgb(242, 242, 242); */
          flex: 1;
          position: relative;

          .coverPic-wrapper {
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--color-black);
            background-repeat: no-repeat;
            background-size: cover;
            /* background-size: 100% auto; */
          }

          .video-btn-wrapper {
            width: 50px;
            height: 50px;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
          }

          .expand-logo {
            position: absolute;
            right: 10px;
            /* bottom: 10px; */
            cursor: pointer;

            &:hover {
              font-weight: 500;
            }
          }
        }

        .video-footer-info {
          height: 40px;
          display: flex;
          align-items: center;
          background-color: #ffffff;
          box-shadow: rgba(170, 170, 170, 0.376) 0px 5px 5px 0px;
          border: 1px solid rgb(242, 242, 242);
          padding-left: 10px;
          width: 100%;
          position: relative;

          .video-footer {
            vertical-align: middle;
            transform: translateY(-3px);
          }

          .footer-logo-wrapper {
            width: 90%;
            display: flex;
            align-items: center;
            position: absolute;
          }

          .footer-title {
            font-size: 18px;
            font-weight: 300;
            color: var(--color-black);
            margin-left: 17px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .header-arrows {
        background-color: #ffffff;
        display: flex;
        align-items: center;
        flex: 1;
        margin-left: 735px;
        .el-icon {
          margin-left: 10px;
          cursor: pointer;
          align-items: center;
        }
        .el-icon svg {
          align-items: center;
        }

        .nav-label {
          font-size: 14px;
          margin: 0 5px;
          border-radius: 5px;
          align-items: center;
        }
        .label-wrapper {
          display: flex;
          align-items: center;
          height: 32px;
          text-align: center;
        }
        .label-wrapper:hover {
          background-color: var(--color-second);
          border-radius: 3px;
        }

        .disabled {
          pointer-events: none;
          color: #ccc;
        }
      }
    }

    .info {
      /* transition: height 0.2s ease-in-out; */
      height: 70px;
      width: 100%;
      background-color: white;
      display: flex;
      margin-bottom: 10px;
      /* justify-content: space-between; */
    }
  }

  @media screen and (max-width: 1200px) {
    .card {
      width: 100%;
      padding-left: 20px;
      padding-right: 20px;
    }

    .tags-wrapper {
      max-width: 50% !important;
    }
  }
}
.right-panel {
  padding-left: 5px;
  width: 45px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.mode-select-simple {
  width: 33px;
  height: 66px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(255, 255, 255, 1);
  box-sizing: border-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(220, 223, 230, 1);
  border-radius: 4px;
  box-shadow: none;
  padding: 2px;
}

.arrow-buttons {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 32px;
  min-height: 400px;
}
.arrow-btn-wrapper {
  width: 33px;
  height: 33px;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}
.arrow-btn-wrapper:hover {
  background: #e0e0e0;
}
.arrow-btn {
  cursor: pointer;
  font-size: 24px;
}
.selected {
  background: rgba(220, 223, 230, 1);
  border-radius: 4px;
}
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 8px;
}
.type-label {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-left: 2px;
}
.ask-btn {
  margin-right: 8px;
}
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  width: 100%;
}
.loading-gif {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
}
.loading-text {
  font-size: 18px;
  color: #888;
  letter-spacing: 2px;
}
</style>
