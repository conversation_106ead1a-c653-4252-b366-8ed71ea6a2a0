<template>
  <div class="test-container">
    <h1>代码容器宽度测试</h1>
    <p>这个页面用于测试代码容器的宽度问题是否已经修复。</p>
    
    <div class="test-section">
      <h2>测试1: 正常长度代码</h2>
      <ContentRenderer :content="normalCode" />
    </div>
    
    <div class="test-section">
      <h2>测试2: 超长代码行</h2>
      <ContentRenderer :content="longCode" />
    </div>
    
    <div class="test-section">
      <h2>测试3: 多行超长代码</h2>
      <ContentRenderer :content="multiLongCode" />
    </div>
  </div>
</template>

<script setup lang="ts">
import ContentRenderer from '@/components/ContentRenderer.vue';

const normalCode = `
<p>这是一段正常的代码：</p>
<pre><code class="language-javascript">
function hello() {
  console.log("Hello World!");
  return true;
}
</code></pre>
`;

const longCode = `
<p>这是一段包含超长行的代码：</p>
<pre><code class="language-javascript">
function processVeryLongFunctionNameWithManyParametersAndComplexLogic(parameterOne, parameterTwo, parameterThree, parameterFour, parameterFive, parameterSix, parameterSeven, parameterEight, parameterNine, parameterTen) {
  const veryLongVariableNameThatExceedsNormalLineLengthAndShouldTriggerHorizontalScrollingOrWrapping = "This is a very long string that should test the horizontal scrolling behavior of the code container";
  return veryLongVariableNameThatExceedsNormalLineLengthAndShouldTriggerHorizontalScrollingOrWrapping;
}
</code></pre>
`;

const multiLongCode = `
<p>这是多行超长代码的测试：</p>
<pre><code class="language-javascript">
// 这是一个非常长的注释行，用来测试代码容器是否能正确处理超长的内容而不会撑开整个页面的宽度，应该出现横向滚动条或者自动换行
function calculateComplexMathematicalOperationWithMultipleParametersAndReturnDetailedResult(firstNumber, secondNumber, thirdNumber, fourthNumber, operationType, precision, roundingMode, errorHandling) {
  const intermediateResultFromFirstCalculation = performComplexCalculationWithFirstSetOfParameters(firstNumber, secondNumber, operationType);
  const intermediateResultFromSecondCalculation = performComplexCalculationWithSecondSetOfParameters(thirdNumber, fourthNumber, operationType);
  const finalResultAfterCombiningAllIntermediateResults = combineResultsAndApplyPrecisionAndRoundingRules(intermediateResultFromFirstCalculation, intermediateResultFromSecondCalculation, precision, roundingMode);
  return handleErrorsAndReturnFinalResultWithProperFormatting(finalResultAfterCombiningAllIntermediateResults, errorHandling);
}
</code></pre>
`;
</script>

<style scoped>
.test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.test-section {
  margin-bottom: 40px;
  border: 2px solid #e0e0e0;
  padding: 20px;
  border-radius: 8px;
}

.test-section h2 {
  color: #333;
  margin-top: 0;
}

/* 模拟实际使用环境的容器约束 */
.test-container {
  /* 限制测试容器宽度，模拟实际页面环境 */
  max-width: 600px;
  overflow-x: hidden;
}
</style>
