{"name": "champaign_frontend", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --host 0.0.0.0", "build": "run-p  build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "prepare": "husky install"}, "lint-staged": {"src/**/*.{js,ts,vue}": ["prettier --write", "eslint", "git add"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@endlessorigin/KsgMap": "^2.0.1", "@endlessorigin/select_to_ask": "0.2.9-alpha.2", "@floating-ui/vue": "^1.0.7", "@highlightjs/vue-plugin": "^2.1.0", "@iktakahiro/markdown-it-katex": "^4.0.1", "animejs": "^3.2.2", "axios": "^1.5.1", "click-outside-vue3": "^4.0.1", "core-js": "^3.8.3", "cos-js-sdk-v5": "^1.8.3", "crypto-js": "^4.2.0", "danmu.js": "^1.1.13", "dat.gui": "^0.7.9", "dom-serializer": "^2.0.0", "domhandler": "^5.0.3", "dompurify": "^3.2.6", "echarts": "^5.6.0", "element-plus": "^2.4.0", "entities": "^6.0.1", "he": "^1.2.0", "highlight.js": "^11.11.1", "htmlparser2": "^10.0.0", "jquery": "^3.7.1", "jquery.mousewheel": "^3.1.9", "jquery.tooltips": "^1.0.0", "js-cookie": "^3.0.5", "katex": "^0.16.21", "ksg-map": "http://**************/bravery-0.2.20.tar.gz", "lodash-es": "^4.17.21", "luxon": "^3.4.3", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "nzh": "^1.0.14", "parse5": "^7.2.0", "pinia": "^2.2.4", "pinia-plugin-persist": "^1.0.0", "qrcode": "^1.5.3", "qrcode.vue": "^3.4.1", "qs": "^6.12.3", "three": "^0.69.0", "tween": "^0.9.0", "url-parse": "^1.5.10", "vditor": "^3.11.0", "vue": "^3.5.11", "vue-cropper": "^1.1.1", "vue-lazyload": "^3.0.0", "vue-router": "^4.2.4", "xgplayer": "^3.0.19", "xgplayer-hls": "^3.0.19", "xgplayer-hls.js": "^3.0.20", "xhook": "^1.6.2"}, "devDependencies": {"@commitlint/cli": "^17.8.0", "@commitlint/config-conventional": "^17.8.0", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/animejs": "^3.1.12", "@types/domhandler": "^2.4.5", "@types/he": "^1.2.3", "@types/htmlparser2": "^3.10.7", "@types/js-cookie": "^3.0.6", "@types/katex": "^0.16.7", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.3.3", "@types/node": "^18.17.17", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.15", "@types/url-parse": "^1.4.11", "@vitejs/plugin-vue": "4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "amfe-flexible": "^2.2.1", "autoprefixer": "^10.4.21", "code-inspector-plugin": "^0.20.10", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.3", "katex": "^0.16.11", "less": "^4.2.0", "lint-staged": "^15.0.1", "npm-run-all2": "^6.0.6", "page-lifecycle": "^0.1.2", "postcss": "^8.5.6", "postcss-pxtorem": "^6.1.0", "prettier": "^3.0.3", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.77.8", "terser": "^5.34.1", "typescript": "~5.2.0", "unplugin-auto-import": "^0.16.6", "unplugin-turbo-console": "^1.10.1", "unplugin-vue-components": "^0.25.2", "url-parse": "^1.5.10", "vite": "^4.4.9", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-mkcert": "^1.17.5", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^1.8.11"}}